package server

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/go-co-op/gocron"
	"github.com/go-kratos/kratos/v2/log"

	"git.minigame.vip/minicloud/service/adsense-bot/internal/biz"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/conf"
	"git.minigame.vip/minicloud/service/adsense-bot/internal/service"
	"git.minigame.vip/minicloud/service/adsense-bot/pkg/bot"
)

type Worker struct {
	scheduler *gocron.Scheduler
	bot       *bot.Client
	log       *log.Helper
}

func NewCronWorker(c *conf.Job, bot *bot.Client, jobService *service.JobService, logger log.Logger) (s *Worker) {
	hlog := log.NewHelper(log.With(logger, "module", "adsense-bot/worker"))
	location, err := time.LoadLocation("Asia/Shanghai")
	if err != nil {
		// 此处如果加载location失败，直接退出进程
		hlog.Fatalf("load location error %v", err)
	}

	jobService.Init()

	s = &Worker{
		scheduler: gocron.NewScheduler(location),
		bot:       bot,
		log:       hlog,
	}

	gocron.SetPanicHandler(func(jobName string, recoverData interface{}) {
		hlog.Info(recoverData)
		err := bot.SendMarkdown(fmt.Sprintf("采集任务%s发生panic,请注意。", jobName))
		if err != nil {
			hlog.Errorf("send panic message failed %v", err)
			return
		}
	})

	for _, j := range c.Jobs {
		jobFunc, ok := service.Jobs[j.Name]
		if !ok {
			hlog.Warnf("can not find job: %s", j.Name)
			continue
		}
		if j.CurrencyCode == "" {
			j.CurrencyCode = biz.USD
		}
		job, err := s.scheduler.Cron(j.Schedule).WhenJobReturnsError(func(jobName string, err error) {
			// 使用配置中的友好任务名称，而不是gocron生成的内部函数名
			friendlyJobName := j.Name
			// 构建详细的失败消息
			var message string
			switch friendlyJobName {
			case "autoCreateSchema-fm":
				message = fmt.Sprintf("自动创建分区表 **%s** <font color=\"warning\">失败</font>\n"+
					"**失败原因:** %v\n"+
					"**时间:** %s\n"+
					"请注意检查。",
					friendlyJobName, err, time.Now().Format("2006-01-02 15:04:05"))
			default:
				// 解析错误信息，提取更多上下文
				errorMsg := err.Error()
				message = fmt.Sprintf("采集任务 **%s** <font color=\"warning\">失败</font>\n"+
					"**执行配置:**\n"+
					"- 货币代码: %s\n"+
					"- 日期范围: %s\n"+
					"- 调度时间: %s\n"+
					"**失败原因:** %s\n"+
					"**建议:** 请检查账号配置和网络连接状态。",
					friendlyJobName, j.CurrencyCode, j.DateRange, j.Schedule, errorMsg)
			}

			err = bot.SendMarkdown(message)
			if err != nil {
				hlog.Errorf("send message failed %v", err)
				return
			}
		}).WhenJobReturnsNoError(func(jobName string) {
			// 成功时不需要额外处理，日志已在 job.go 中记录
		}).Do(jobFunc, context.Background(), j)
		if err != nil {
			hlog.Errorf("start job %v failed %v", job, err)
			panic(err)
		}
	}
	return s
}

func (s *Worker) Start(c context.Context) error {
	log.Info("start job worker")
	s.scheduler.StartAsync()
	log.Info(s.scheduler.NextRun())

	return nil
}

func (s *Worker) Stop(c context.Context) error {
	s.scheduler.Stop()
	log.Info("stop job worker")
	return nil
}
